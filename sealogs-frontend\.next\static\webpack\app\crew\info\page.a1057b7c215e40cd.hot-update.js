"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/view.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/view.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CrewView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _crew_training_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../crew-training/list */ \"(app-pages-browser)/./src/app/ui/crew-training/list.tsx\");\n/* harmony import */ var _crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../crew/allocated-tasks */ \"(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\");\n/* harmony import */ var _crew_voyages__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../crew/voyages */ \"(app-pages-browser)/./src/app/ui/crew/voyages.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/maintenanceHelper */ \"(app-pages-browser)/./src/app/helpers/maintenanceHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* ---------- shadcn/ui replacements ------------------------------------ */ \n\n\n/* ---------------------------------------------------------------------- */ function CrewView(param) {\n    let { crewId } = param;\n    var _crewInfo_vehicles, _crewInfo_departments;\n    _s();\n    /* ---------------- state / helpers ----------------------------------- */ const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"tab\", {\n        defaultValue: \"training\"\n    });\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dueTrainingCounter, setDueTrainingCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [taskList, setTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [voyages, setVoyages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSelf, setIsSelf] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [archiveOpen, setArchiveOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /* ---------------- data fetch ---------------------------------------- */ (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewMembersLogBookEntrySections)(crewId, setVoyages);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n    }, []);\n    /* vessels ------------------------------------------------------------ */ const handleSetVessels = (vsls)=>{\n        const activeVessels = vsls.filter((v)=>!v.archived);\n        setVessels(activeVessels.map((v)=>({\n                label: v.title,\n                value: v.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getVesselList)(handleSetVessels);\n    /* tasks -------------------------------------------------------------- */ const handleSetTaskList = (tasks)=>{\n        const active = tasks.filter((t)=>!t.archived).map((t)=>({\n                ...t,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.isOverDueTask)(t)\n            }));\n        const list = (0,_app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__.sortMaintenanceChecks)(active.map((mc)=>({\n                id: mc.id,\n                name: mc.name,\n                basicComponentID: mc.basicComponentID,\n                comments: mc.comments,\n                description: mc.description,\n                assignedToID: mc.assignedToID,\n                expires: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.upcomingScheduleDate)(mc),\n                status: mc.status,\n                startDate: mc.startDate,\n                isOverDue: mc.isOverDue,\n                basicComponent: mc.basicComponent,\n                isCompleted: mc.status === \"Completed\" ? \"1\" : \"2\"\n            })));\n        setTaskList(list);\n        setTaskCounter(active.filter((t)=>![\n                \"Completed\",\n                \"Save_As_Draft\"\n            ].includes(t.status) && ![\n                \"Completed\",\n                \"Upcoming\"\n            ].includes(t.isOverDue.status)).length);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getComponentMaintenanceCheckByMemberId)(crewId, handleSetTaskList);\n    /* crew info ---------------------------------------------------------- */ const handleSetCrewInfo = (info)=>{\n        var _withTraining_trainingSessionsDue;\n        setCrewInfo(info);\n        const [withTraining] = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            info\n        ], vessels);\n        var _withTraining_trainingSessionsDue_nodes_filter;\n        const dues = (_withTraining_trainingSessionsDue_nodes_filter = withTraining === null || withTraining === void 0 ? void 0 : (_withTraining_trainingSessionsDue = withTraining.trainingSessionsDue) === null || _withTraining_trainingSessionsDue === void 0 ? void 0 : _withTraining_trainingSessionsDue.nodes.filter((n)=>n.status.isOverdue || n.status.dueWithinSevenDays)) !== null && _withTraining_trainingSessionsDue_nodes_filter !== void 0 ? _withTraining_trainingSessionsDue_nodes_filter : [];\n        setDueTrainingCounter(dues.length);\n        if (localStorage.getItem(\"userId\") === info.id) setIsSelf(true);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewByID)(crewId, handleSetCrewInfo);\n    /* archive / retrieve user ------------------------------------------- */ const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>router.back(),\n        onError: (err)=>console.error(\"mutationUpdateUser error\", err)\n    });\n    const handleArchiveUser = async (info)=>{\n        if (!(info && info.id > 0)) return;\n        await mutationUpdateUser({\n            variables: {\n                input: {\n                    id: info.id,\n                    isArchived: !info.isArchived\n                }\n            }\n        });\n    };\n    /* permission helpers ------------------------------------------------- */ const noPerm = (perm)=>!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(perm, permissions);\n    const BadgeCounter = (param)=>{\n        let { count } = param;\n        return count ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"ml-2 flex h-5 w-5 items-center justify-center rounded-full border border-rose-600 bg-rose-100 text-xs font-medium text-rose-600\",\n            children: count\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 164,\n            columnNumber: 13\n        }, this) : null;\n    };\n    /* early exit if no access ------------------------------------------- */ if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER_CONTACT\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 176,\n            columnNumber: 13\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 178,\n            columnNumber: 13\n        }, this);\n    }\n    /* active log-book ---------------------------------------------------- */ const activeLog = voyages && voyages.length > 0 && !voyages[0].punchOut ? voyages[0] : null;\n    /* ----------------------- render ------------------------------------ */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col justify-between md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.H2, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 font-medium\",\n                                children: \"Crew:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1\",\n                                children: !crewInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__.Skeleton, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 29\n                                }, this) : \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: crewInfo.isArchived ? \"warning\" : activeLog ? \"warning\" : \"primary\",\n                                className: \"hidden min-w-fit rounded h-fit py-0.5 px-1.5 text-sm font-normal lg:inline ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    className: \"text-fire-bush-700 hover:text-fire-bush-800\",\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: crewInfo.isArchived ? \"warning\" : activeLog ? \"warning\" : \"primary\",\n                                className: \"block w-max rounded py-0.5 px-1.5 text-sm font-normal lg:hidden mb-2 ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    className: \"text-fire-bush-700 hover:text-fire-bush-800\",\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center justify-end gap-2\",\n                        children: [\n                            permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                                open: archiveOpen,\n                                onOpenChange: setArchiveOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            variant: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"primary\" : \"destructive\",\n                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                        className: \"sm:max-w-md\",\n                                        children: (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.DELETE_MEMBER || \"DELETE_MEMBER\", permissions) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: [\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\",\n                                                                \" \",\n                                                                \"User\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogDescription, {\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"retrieve\" : \"archive\",\n                                                                \" \",\n                                                                \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"this user\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\"),\n                                                                \" \",\n                                                                \"?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end gap-2 pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: \"outline\",\n                                                            onClick: ()=>setArchiveOpen(false),\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"primary\" : \"destructive\",\n                                                            onClick: ()=>{\n                                                                handleArchiveUser(crewInfo);\n                                                                setArchiveOpen(false);\n                                                            },\n                                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                        children: \"Warning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-3 text-slate-500\",\n                                                    children: \"You do not have permission to archive user.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setArchiveOpen(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 29\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/edit?id=\".concat(crewId)),\n                                children: \"Edit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 25\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/create\"),\n                                className: \"\".concat(tab === \"training\" ? \"hidden\" : \"\", \" \").concat(tab === \"qualification\" ? \"!mr-0\" : \"\"),\n                                children: \"Add Qualification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 25\n                            }, this),\n                            permissions && tab !== \"qualification\" && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/crew-training/create?memberId=\".concat(crewId)),\n                                children: \"Record Training\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 192,\n                columnNumber: 13\n            }, this),\n            ((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.email) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.vehicles) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.phoneNumber)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[1px] mt-2 mb-3 border-t border-b border-border px-4 pb-4 pt-4\",\n                children: [\n                    (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.primaryDuty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Primary Duty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ms-2\",\n                                children: crewInfo.primaryDuty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 25\n                    }, this),\n                    [\n                        \"email\",\n                        \"phoneNumber\"\n                    ].map((field)=>(permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.VIEW_MEMBER_CONTACT || \"VIEW_MEMBER_CONTACT\", permissions) || isSelf) && (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo[field]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-4 w-32\",\n                                    children: field === \"email\" ? \"Email:\" : \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-2\",\n                                    children: crewInfo[field]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, field, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 33\n                        }, this)),\n                    ((_crewInfo_vehicles = crewInfo.vehicles) === null || _crewInfo_vehicles === void 0 ? void 0 : _crewInfo_vehicles.nodes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Vessels:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap flex-wrap md:flex-nowrap\",\n                                children: crewInfo.vehicles.nodes.map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(v.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                            className: \"w-fit h-fit rounded-lg\",\n                                            children: v.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, v.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 25\n                    }, this),\n                    ((_crewInfo_departments = crewInfo.departments) === null || _crewInfo_departments === void 0 ? void 0 : _crewInfo_departments.nodes) && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Departments:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap md:flex-nowrap\",\n                                children: crewInfo.departments.nodes.map((d)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/department/info?id=\".concat(d.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ms-2 my-1 rounded border py-1 px-2 md:my-0\",\n                                            children: d.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, d.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 45\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 29\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 399,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.Tabs, {\n                    value: tab,\n                    onValueChange: setTab,\n                    className: \"pt-2 pb-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsList, {\n                            className: \"gap-2\",\n                            children: [\n                                (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"training\",\n                                    children: [\n                                        \"Training\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: dueTrainingCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                                    value: \"qualification\",\n                                                    disabled: true,\n                                                    children: \"Qualifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                            side: \"bottom\",\n                                            children: \"Coming soon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"allocatedTasks\",\n                                    children: [\n                                        \"Allocated Tasks\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: taskCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"voyages\",\n                                    children: \"Voyages\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"training\",\n                            children: noPerm(\"VIEW_MEMBER_TRAINING\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_list__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                memberId: crewId,\n                                excludeFilters: [\n                                    \"crew\",\n                                    \"overdueToggle\"\n                                ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"qualification\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"allocatedTasks\",\n                            children: noPerm(\"VIEW_MEMBER_TASKS\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                taskList: taskList\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"voyages\",\n                            children: noPerm(\"VIEW_MEMBER_VOYAGES\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_voyages__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                voyages: voyages\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 477,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n        lineNumber: 190,\n        columnNumber: 9\n    }, this);\n}\n_s(CrewView, \"A7VUUPb+Tfb3R8d4PeyBGNikOac=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = CrewView;\nvar _c;\n$RefreshReg$(_c, \"CrewView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/view.tsx\n"));

/***/ })

});