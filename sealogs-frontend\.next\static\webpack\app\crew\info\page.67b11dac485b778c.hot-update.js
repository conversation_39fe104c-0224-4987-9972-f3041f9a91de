"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/view.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/view.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CrewView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _crew_training_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../crew-training/list */ \"(app-pages-browser)/./src/app/ui/crew-training/list.tsx\");\n/* harmony import */ var _crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../crew/allocated-tasks */ \"(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\");\n/* harmony import */ var _crew_voyages__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../crew/voyages */ \"(app-pages-browser)/./src/app/ui/crew/voyages.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/maintenanceHelper */ \"(app-pages-browser)/./src/app/helpers/maintenanceHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* ---------- shadcn/ui replacements ------------------------------------ */ \n\n\n/* ---------------------------------------------------------------------- */ function CrewView(param) {\n    let { crewId } = param;\n    var _crewInfo_vehicles, _crewInfo_departments;\n    _s();\n    /* ---------------- state / helpers ----------------------------------- */ const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"tab\", {\n        defaultValue: \"training\"\n    });\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dueTrainingCounter, setDueTrainingCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [taskList, setTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [voyages, setVoyages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSelf, setIsSelf] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [archiveOpen, setArchiveOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /* ---------------- data fetch ---------------------------------------- */ (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewMembersLogBookEntrySections)(crewId, setVoyages);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n    }, []);\n    /* vessels ------------------------------------------------------------ */ const handleSetVessels = (vsls)=>{\n        const activeVessels = vsls.filter((v)=>!v.archived);\n        setVessels(activeVessels.map((v)=>({\n                label: v.title,\n                value: v.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getVesselList)(handleSetVessels);\n    /* tasks -------------------------------------------------------------- */ const handleSetTaskList = (tasks)=>{\n        const active = tasks.filter((t)=>!t.archived).map((t)=>({\n                ...t,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.isOverDueTask)(t)\n            }));\n        const list = (0,_app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__.sortMaintenanceChecks)(active.map((mc)=>({\n                id: mc.id,\n                name: mc.name,\n                basicComponentID: mc.basicComponentID,\n                comments: mc.comments,\n                description: mc.description,\n                assignedToID: mc.assignedToID,\n                expires: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.upcomingScheduleDate)(mc),\n                status: mc.status,\n                startDate: mc.startDate,\n                isOverDue: mc.isOverDue,\n                basicComponent: mc.basicComponent,\n                isCompleted: mc.status === \"Completed\" ? \"1\" : \"2\"\n            })));\n        setTaskList(list);\n        setTaskCounter(active.filter((t)=>![\n                \"Completed\",\n                \"Save_As_Draft\"\n            ].includes(t.status) && ![\n                \"Completed\",\n                \"Upcoming\"\n            ].includes(t.isOverDue.status)).length);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getComponentMaintenanceCheckByMemberId)(crewId, handleSetTaskList);\n    /* crew info ---------------------------------------------------------- */ const handleSetCrewInfo = (info)=>{\n        var _withTraining_trainingSessionsDue;\n        setCrewInfo(info);\n        const [withTraining] = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            info\n        ], vessels);\n        var _withTraining_trainingSessionsDue_nodes_filter;\n        const dues = (_withTraining_trainingSessionsDue_nodes_filter = withTraining === null || withTraining === void 0 ? void 0 : (_withTraining_trainingSessionsDue = withTraining.trainingSessionsDue) === null || _withTraining_trainingSessionsDue === void 0 ? void 0 : _withTraining_trainingSessionsDue.nodes.filter((n)=>n.status.isOverdue || n.status.dueWithinSevenDays)) !== null && _withTraining_trainingSessionsDue_nodes_filter !== void 0 ? _withTraining_trainingSessionsDue_nodes_filter : [];\n        setDueTrainingCounter(dues.length);\n        if (localStorage.getItem(\"userId\") === info.id) setIsSelf(true);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewByID)(crewId, handleSetCrewInfo);\n    /* archive / retrieve user ------------------------------------------- */ const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>router.back(),\n        onError: (err)=>console.error(\"mutationUpdateUser error\", err)\n    });\n    const handleArchiveUser = async (info)=>{\n        if (!(info && info.id > 0)) return;\n        await mutationUpdateUser({\n            variables: {\n                input: {\n                    id: info.id,\n                    isArchived: !info.isArchived\n                }\n            }\n        });\n    };\n    /* permission helpers ------------------------------------------------- */ const noPerm = (perm)=>!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(perm, permissions);\n    const BadgeCounter = (param)=>{\n        let { count } = param;\n        return count ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"ml-2 flex h-5 w-5 items-center justify-center rounded-full border border-rose-600 bg-rose-100 text-xs font-medium text-rose-600\",\n            children: count\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 164,\n            columnNumber: 13\n        }, this) : null;\n    };\n    /* early exit if no access ------------------------------------------- */ if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER_CONTACT\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 176,\n            columnNumber: 13\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 178,\n            columnNumber: 13\n        }, this);\n    }\n    /* active log-book ---------------------------------------------------- */ const activeLog = voyages && voyages.length > 0 && !voyages[0].punchOut ? voyages[0] : null;\n    /* ----------------------- render ------------------------------------ */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col justify-between md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.H2, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 font-medium\",\n                                children: \"Crew:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1\",\n                                children: !crewInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__.Skeleton, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 29\n                                }, this) : \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: crewInfo.isArchived ? \"warning\" : activeLog ? \"warning\" : \"primary\",\n                                className: \"hidden min-w-fit rounded h-fit py-0.5 px-1.5 text-sm font-normal lg:inline ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    className: \"text-fire-bush-700 hover:text-fire-bush-800\",\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: crewInfo.isArchived ? \"warning\" : activeLog ? \"warning\" : \"primary\",\n                                className: \"block w-max rounded py-0.5 px-1.5 text-sm font-normal lg:hidden mb-2 ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    className: \"text-fire-bush-700 hover:text-fire-bush-800\",\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center justify-end gap-2\",\n                        children: [\n                            permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                                open: archiveOpen,\n                                onOpenChange: setArchiveOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            variant: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"primary\" : \"\",\n                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                        className: \"sm:max-w-md\",\n                                        children: (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.DELETE_MEMBER || \"DELETE_MEMBER\", permissions) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: [\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\",\n                                                                \" \",\n                                                                \"User\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogDescription, {\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"retrieve\" : \"archive\",\n                                                                \" \",\n                                                                \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"this user\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\"),\n                                                                \" \",\n                                                                \"?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end gap-2 pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: \"outline\",\n                                                            onClick: ()=>setArchiveOpen(false),\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"primary\" : \"destructive\",\n                                                            onClick: ()=>{\n                                                                handleArchiveUser(crewInfo);\n                                                                setArchiveOpen(false);\n                                                            },\n                                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                        children: \"Warning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-3 text-slate-500\",\n                                                    children: \"You do not have permission to archive user.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setArchiveOpen(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 29\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/edit?id=\".concat(crewId)),\n                                children: \"Edit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 25\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/create\"),\n                                className: \"\".concat(tab === \"training\" ? \"hidden\" : \"\", \" \").concat(tab === \"qualification\" ? \"!mr-0\" : \"\"),\n                                children: \"Add Qualification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 25\n                            }, this),\n                            permissions && tab !== \"qualification\" && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/crew-training/create?memberId=\".concat(crewId)),\n                                children: \"Record Training\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 192,\n                columnNumber: 13\n            }, this),\n            ((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.email) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.vehicles) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.phoneNumber)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[1px] mt-2 mb-3 border-t border-b border-border px-4 pb-4 pt-4\",\n                children: [\n                    (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.primaryDuty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Primary Duty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ms-2\",\n                                children: crewInfo.primaryDuty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 25\n                    }, this),\n                    [\n                        \"email\",\n                        \"phoneNumber\"\n                    ].map((field)=>(permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.VIEW_MEMBER_CONTACT || \"VIEW_MEMBER_CONTACT\", permissions) || isSelf) && (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo[field]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-4 w-32\",\n                                    children: field === \"email\" ? \"Email:\" : \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-2\",\n                                    children: crewInfo[field]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, field, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 33\n                        }, this)),\n                    ((_crewInfo_vehicles = crewInfo.vehicles) === null || _crewInfo_vehicles === void 0 ? void 0 : _crewInfo_vehicles.nodes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Vessels:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 flex-wrap md:flex-nowrap\",\n                                children: crewInfo.vehicles.nodes.map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(v.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                            variant: \"primary\",\n                                            className: \"w-fit h-fit py-2 rounded-lg\",\n                                            children: v.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, v.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 25\n                    }, this),\n                    ((_crewInfo_departments = crewInfo.departments) === null || _crewInfo_departments === void 0 ? void 0 : _crewInfo_departments.nodes) && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Departments:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap md:flex-nowrap\",\n                                children: crewInfo.departments.nodes.map((d)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/department/info?id=\".concat(d.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ms-2 my-1 rounded border py-1 px-2 md:my-0\",\n                                            children: d.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, d.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 45\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 29\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 399,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.Tabs, {\n                    value: tab,\n                    onValueChange: setTab,\n                    className: \"pt-2 pb-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsList, {\n                            className: \"gap-2\",\n                            children: [\n                                (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"training\",\n                                    children: [\n                                        \"Training\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: dueTrainingCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                                    value: \"qualification\",\n                                                    disabled: true,\n                                                    children: \"Qualifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                            side: \"bottom\",\n                                            children: \"Coming soon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"allocatedTasks\",\n                                    children: [\n                                        \"Allocated Tasks\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: taskCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"voyages\",\n                                    children: \"Voyages\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"training\",\n                            children: noPerm(\"VIEW_MEMBER_TRAINING\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_list__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                memberId: crewId,\n                                excludeFilters: [\n                                    \"crew\",\n                                    \"overdueToggle\"\n                                ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"qualification\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"allocatedTasks\",\n                            children: noPerm(\"VIEW_MEMBER_TASKS\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                taskList: taskList\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"voyages\",\n                            children: noPerm(\"VIEW_MEMBER_VOYAGES\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_voyages__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                voyages: voyages\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 477,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n        lineNumber: 190,\n        columnNumber: 9\n    }, this);\n}\n_s(CrewView, \"A7VUUPb+Tfb3R8d4PeyBGNikOac=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = CrewView;\nvar _c;\n$RefreshReg$(_c, \"CrewView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/view.tsx\n"));

/***/ })

});