'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useMutation } from '@apollo/client'
import { useQueryState } from 'nuqs'

import { UPDATE_USER } from '@/app/lib/graphQL/mutation'
import CrewTrainingList from '../crew-training/list'
import CrewAllocatedTasks from '../crew/allocated-tasks'
import CrewVoyages from '../crew/voyages'

import {
    getVesselList,
    getComponentMaintenanceCheckByMemberId,
    isOverDueTask,
    getCrewMembersLogBookEntrySections,
    GetCrewListWithTrainingStatus,
    upcomingScheduleDate,
    getCrewByID,
} from '@/app/lib/actions'

import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { sortMaintenanceChecks } from '@/app/helpers/maintenanceHelper'

import Loading from '@/app/loading'
import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON><PERSON>, <PERSON><PERSON>, H2 } from '@/components/ui'

/* ---------- shadcn/ui replacements ------------------------------------ */
import {
    Dialog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from '@/components/ui/dialog'

import {
    TooltipProvider,
    Tooltip,
    TooltipTrigger,
    TooltipContent,
} from '@/components/ui/tooltip'

import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
/* ---------------------------------------------------------------------- */

export default function CrewView({ crewId }: { crewId: number }) {
    /* ---------------- state / helpers ----------------------------------- */
    const router = useRouter()

    // Use nuqs to manage the tab state through URL query parameters
    const [tab, setTab] = useQueryState('tab', {
        defaultValue: 'training',
    })

    const [taskCounter, setTaskCounter] = useState(0)
    const [dueTrainingCounter, setDueTrainingCounter] = useState(0)

    const [vessels, setVessels] = useState<
        Array<{ label: string; value: number }>
    >([])

    const [taskList, setTaskList] = useState<any[]>([])
    const [voyages, setVoyages] = useState<any[]>([])

    const [permissions, setPermissions] = useState<any>(false)
    const [isSelf, setIsSelf] = useState(false)

    const [crewInfo, setCrewInfo] = useState<any>({})
    const [archiveOpen, setArchiveOpen] = useState(false)

    /* ---------------- data fetch ---------------------------------------- */
    getCrewMembersLogBookEntrySections(crewId, setVoyages)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    /* vessels ------------------------------------------------------------ */
    const handleSetVessels = (vsls: any) => {
        const activeVessels = vsls.filter((v: any) => !v.archived)
        setVessels(
            activeVessels.map((v: any) => ({ label: v.title, value: v.id })),
        )
    }
    getVesselList(handleSetVessels)

    /* tasks -------------------------------------------------------------- */
    const handleSetTaskList = (tasks: any[]) => {
        const active = tasks
            .filter((t) => !t.archived)
            .map((t) => ({ ...t, isOverDue: isOverDueTask(t) }))

        const list = sortMaintenanceChecks(
            active.map((mc) => ({
                id: mc.id,
                name: mc.name,
                basicComponentID: mc.basicComponentID,
                comments: mc.comments,
                description: mc.description,
                assignedToID: mc.assignedToID,
                expires: upcomingScheduleDate(mc),
                status: mc.status,
                startDate: mc.startDate,
                isOverDue: mc.isOverDue,
                basicComponent: mc.basicComponent,
                isCompleted: mc.status === 'Completed' ? '1' : '2',
            })),
        )

        setTaskList(list)

        setTaskCounter(
            active.filter(
                (t) =>
                    !['Completed', 'Save_As_Draft'].includes(t.status) &&
                    !['Completed', 'Upcoming'].includes(t.isOverDue.status),
            ).length,
        )
    }
    getComponentMaintenanceCheckByMemberId(crewId, handleSetTaskList)

    /* crew info ---------------------------------------------------------- */
    const handleSetCrewInfo = (info: any) => {
        setCrewInfo(info)

        const [withTraining] = GetCrewListWithTrainingStatus([info], vessels)
        const dues =
            withTraining?.trainingSessionsDue?.nodes.filter(
                (n: any) => n.status.isOverdue || n.status.dueWithinSevenDays,
            ) ?? []

        setDueTrainingCounter(dues.length)
        if (localStorage.getItem('userId') === info.id) setIsSelf(true)
    }
    getCrewByID(crewId, handleSetCrewInfo)

    /* archive / retrieve user ------------------------------------------- */
    const [mutationUpdateUser] = useMutation(UPDATE_USER, {
        onCompleted: () => router.back(),
        onError: (err) => console.error('mutationUpdateUser error', err),
    })

    const handleArchiveUser = async (info: any) => {
        if (!(info && info.id > 0)) return
        await mutationUpdateUser({
            variables: {
                input: { id: info.id, isArchived: !info.isArchived },
            },
        })
    }

    /* permission helpers ------------------------------------------------- */
    const noPerm = (perm: string) =>
        !permissions || !hasPermission(perm, permissions)

    const BadgeCounter = ({ count }: { count: number }) =>
        count ? (
            <span className="ml-2 flex h-5 w-5 items-center justify-center rounded-full border border-rose-600 bg-rose-100 text-xs font-medium text-rose-600">
                {count}
            </span>
        ) : null

    /* early exit if no access ------------------------------------------- */
    if (
        !permissions ||
        (!hasPermission('VIEW_MEMBER', permissions) &&
            !hasPermission('VIEW_MEMBER_CONTACT', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops! You do not have the permission to view this section." />
        )
    }

    /* active log-book ---------------------------------------------------- */
    const activeLog =
        voyages && voyages.length > 0 && !voyages[0].punchOut
            ? voyages[0]
            : null

    /* ----------------------- render ------------------------------------ */
    return (
        <div className="w-full p-0">
            {/* ------------------------------------------------ header -------- */}
            <div className="flex flex-col justify-between md:flex-row">
                <H2 className="flex items-center gap-2">
                    <span className="mr-2 font-medium">Crew:</span>
                    <span className="flex-1">
                        {!crewInfo ? (
                            <Skeleton />
                        ) : (
                            `${crewInfo?.firstName || ''} ${crewInfo?.surname || ''}`
                        )}
                    </span>

                    {/* status badges */}
                    <Badge
                        variant={
                            crewInfo.isArchived
                                ? 'warning'
                                : activeLog
                                  ? 'warning'
                                  : 'primary'
                        }
                        className="hidden min-w-fit rounded h-fit py-0.5 px-1.5 text-sm font-normal lg:inline ms-2">
                        {crewInfo.isArchived ? (
                            'Archived'
                        ) : activeLog ? (
                            <Link
                                href={`/log-entries/view?&vesselID=${activeLog.logBookEntry.vehicle.id}&logentryID=${activeLog.logBookEntry.id}`}
                                className="text-fire-bush-700 hover:text-fire-bush-800">
                                Active log book at{' '}
                                {activeLog.logBookEntry.vehicle.title}
                            </Link>
                        ) : (
                            'No active log books'
                        )}
                    </Badge>

                    {/* mobile badge */}
                    <Badge
                        variant={
                            crewInfo.isArchived
                                ? 'warning'
                                : activeLog
                                  ? 'warning'
                                  : 'primary'
                        }
                        className="block w-max rounded py-0.5 px-1.5 text-sm font-normal lg:hidden mb-2 ms-2">
                        {crewInfo.isArchived ? (
                            'Archived'
                        ) : activeLog ? (
                            <Link
                                href={`/log-entries/view?&vesselID=${activeLog.logBookEntry.vehicle.id}&logentryID=${activeLog.logBookEntry.id}`}
                                className="text-fire-bush-700 hover:text-fire-bush-800">
                                Active log book at{' '}
                                {activeLog.logBookEntry.vehicle.title}
                            </Link>
                        ) : (
                            'No active log books'
                        )}
                    </Badge>
                </H2>

                {/* ------------------------- action buttons --------------------- */}
                <div className="flex flex-wrap items-center justify-end gap-2">
                    {permissions &&
                        hasPermission(
                            process.env.EDIT_MEMBER || 'EDIT_MEMBER',
                            permissions,
                        ) && (
                            <Dialog
                                open={archiveOpen}
                                onOpenChange={setArchiveOpen}>
                                <DialogTrigger asChild>
                                    <Button
                                        variant={
                                            crewInfo?.isArchived
                                                ? 'primary'
                                                : 'destructive'
                                        }>
                                        {crewInfo?.isArchived
                                            ? 'Retrieve'
                                            : 'Archive'}
                                    </Button>
                                </DialogTrigger>

                                <DialogContent className="sm:max-w-md">
                                    {hasPermission(
                                        process.env.DELETE_MEMBER ||
                                            'DELETE_MEMBER',
                                        permissions,
                                    ) ? (
                                        <>
                                            <DialogHeader>
                                                <DialogTitle className="text-2xl">
                                                    {crewInfo?.isArchived
                                                        ? 'Retrieve'
                                                        : 'Archive'}{' '}
                                                    User
                                                </DialogTitle>
                                                <DialogDescription>
                                                    Are you sure you want to{' '}
                                                    {crewInfo?.isArchived
                                                        ? 'retrieve'
                                                        : 'archive'}{' '}
                                                    {`${crewInfo?.firstName || 'this user'} ${crewInfo?.surname || ''}`}{' '}
                                                    ?
                                                </DialogDescription>
                                            </DialogHeader>

                                            <DialogFooter className="flex justify-end gap-2 pt-6">
                                                <Button
                                                    variant="outline"
                                                    onClick={() =>
                                                        setArchiveOpen(false)
                                                    }>
                                                    Cancel
                                                </Button>
                                                <Button
                                                    variant={
                                                        crewInfo?.isArchived
                                                            ? 'primary'
                                                            : 'destructive'
                                                    }
                                                    onClick={() => {
                                                        handleArchiveUser(
                                                            crewInfo,
                                                        )
                                                        setArchiveOpen(false)
                                                    }}>
                                                    {crewInfo?.isArchived
                                                        ? 'Retrieve'
                                                        : 'Archive'}
                                                </Button>
                                            </DialogFooter>
                                        </>
                                    ) : (
                                        <>
                                            <DialogHeader>
                                                <DialogTitle>
                                                    Warning
                                                </DialogTitle>
                                            </DialogHeader>
                                            <p className="mt-3 text-slate-500">
                                                You do not have permission to
                                                archive user.
                                            </p>
                                            <DialogFooter className="flex justify-end pt-6">
                                                <Button
                                                    variant="outline"
                                                    onClick={() =>
                                                        setArchiveOpen(false)
                                                    }>
                                                    Cancel
                                                </Button>
                                            </DialogFooter>
                                        </>
                                    )}
                                </DialogContent>
                            </Dialog>
                        )}

                    {((permissions &&
                        hasPermission(
                            process.env.EDIT_MEMBER || 'EDIT_MEMBER',
                            permissions,
                        )) ||
                        isSelf) && (
                        <Button
                            onClick={() =>
                                router.push(`/user/edit?id=${crewId}`)
                            }>
                            Edit
                        </Button>
                    )}

                    {((permissions &&
                        hasPermission(
                            process.env.EDIT_MEMBER || 'EDIT_MEMBER',
                            permissions,
                        )) ||
                        isSelf) && (
                        <Button
                            onClick={() => router.push(`/user/create`)}
                            className={`${tab === 'training' ? 'hidden' : ''} ${
                                tab === 'qualification' ? '!mr-0' : ''
                            }`}>
                            Add Qualification
                        </Button>
                    )}

                    {permissions &&
                        tab !== 'qualification' &&
                        hasPermission('RECORD_TRAINING', permissions) && (
                            <Button
                                onClick={() =>
                                    router.push(
                                        `/crew-training/create?memberId=${crewId}`,
                                    )
                                }>
                                Record Training
                            </Button>
                        )}
                </div>
            </div>

            {/* ----------------------- Contact & meta block -------------------- */}
            {(crewInfo?.email ||
                crewInfo?.vehicles ||
                crewInfo?.phoneNumber) && (
                <div className="ml-[1px] mt-2 mb-3 border-t border-b border-border px-4 pb-4 pt-4">
                    {crewInfo?.primaryDuty && (
                        <div className="mt-2 flex items-center">
                            <span className="mr-4 w-32">Primary Duty:</span>
                            <span className="ms-2">
                                {crewInfo.primaryDuty.title}
                            </span>
                        </div>
                    )}

                    {/* email / phone */}
                    {['email', 'phoneNumber'].map(
                        (field) =>
                            ((permissions &&
                                hasPermission(
                                    process.env.VIEW_MEMBER_CONTACT ||
                                        'VIEW_MEMBER_CONTACT',
                                    permissions,
                                )) ||
                                isSelf) &&
                            crewInfo?.[field] && (
                                <div
                                    key={field}
                                    className="mt-4 flex items-center">
                                    <span className="mr-4 w-32">
                                        {field === 'email'
                                            ? 'Email:'
                                            : 'Phone:'}
                                    </span>
                                    <span className="ms-2">
                                        {crewInfo[field]}
                                    </span>
                                </div>
                            ),
                    )}

                    {/* vessels */}
                    {crewInfo.vehicles?.nodes && (
                        <div className="mb-2 mt-4 flex items-center">
                            <span className="mr-4 w-32">Vessels:</span>
                            <div className="flex gap-2 flex-wrap md:flex-nowrap">
                                {crewInfo.vehicles.nodes.map((v: any) => (
                                    <Link
                                        key={v.id}
                                        href={`/vessel/info?id=${v.id}`}>
                                        <Badge variant='primary' className="w-fit h-fit py-2 rounded-lg">
                                            {v.title}
                                        </Badge>
                                    </Link>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* departments (optional) */}
                    {crewInfo.departments?.nodes &&
                        localStorage.getItem('useDepartment') === 'true' && (
                            <div className="mb-2 mt-4 flex items-center">
                                <span className="mr-4 w-32">Departments:</span>
                                <div className="flex flex-wrap md:flex-nowrap">
                                    {crewInfo.departments.nodes.map(
                                        (d: any) => (
                                            <Link
                                                key={d.id}
                                                href={`/department/info?id=${d.id}`}>
                                                <div className="ms-2 my-1 rounded border py-1 px-2 md:my-0">
                                                    {d.title}
                                                </div>
                                            </Link>
                                        ),
                                    )}
                                </div>
                            </div>
                        )}
                </div>
            )}

            {/* ----------------------- Tabs ------------------------------------ */}
            <TooltipProvider>
                <Tabs value={tab} onValueChange={setTab} className="pt-2 pb-5">
                    <TabsList className="gap-2">
                        {hasPermission('VIEW_TRAINING', permissions) && (
                            <TabsTrigger value="training">
                                Training
                                <BadgeCounter count={dueTrainingCounter} />
                            </TabsTrigger>
                        )}

                        <Tooltip>
                            <TooltipTrigger asChild>
                                <span className="inline-block">
                                    <TabsTrigger value="qualification" disabled>
                                        Qualifications
                                    </TabsTrigger>
                                </span>
                            </TooltipTrigger>
                            <TooltipContent side="bottom">
                                Coming soon
                            </TooltipContent>
                        </Tooltip>

                        <TabsTrigger value="allocatedTasks">
                            Allocated Tasks
                            <BadgeCounter count={taskCounter} />
                        </TabsTrigger>

                        <TabsTrigger value="voyages">Voyages</TabsTrigger>
                    </TabsList>

                    {/* --------------- tab content ------------------------------ */}
                    <TabsContent value="training">
                        {noPerm('VIEW_MEMBER_TRAINING') ? (
                            <Loading errorMessage="Oops! You do not have permission to view this section." />
                        ) : (
                            <CrewTrainingList
                                memberId={crewId}
                                excludeFilters={['crew', 'overdueToggle']}
                            />
                        )}
                    </TabsContent>

                    <TabsContent value="qualification" />

                    <TabsContent value="allocatedTasks">
                        {noPerm('VIEW_MEMBER_TASKS') ? (
                            <Loading errorMessage="Oops! You do not have permission to view this section." />
                        ) : (
                            <CrewAllocatedTasks taskList={taskList} />
                        )}
                    </TabsContent>

                    <TabsContent value="voyages">
                        {noPerm('VIEW_MEMBER_VOYAGES') ? (
                            <Loading errorMessage="Oops! You do not have permission to view this section." />
                        ) : (
                            <CrewVoyages voyages={voyages} />
                        )}
                    </TabsContent>
                </Tabs>
            </TooltipProvider>
        </div>
    )
}
