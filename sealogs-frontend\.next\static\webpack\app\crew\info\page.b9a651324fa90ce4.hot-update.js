"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/view.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/view.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CrewView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _crew_training_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../crew-training/list */ \"(app-pages-browser)/./src/app/ui/crew-training/list.tsx\");\n/* harmony import */ var _crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../crew/allocated-tasks */ \"(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\");\n/* harmony import */ var _crew_voyages__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../crew/voyages */ \"(app-pages-browser)/./src/app/ui/crew/voyages.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/maintenanceHelper */ \"(app-pages-browser)/./src/app/helpers/maintenanceHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* ---------- shadcn/ui replacements ------------------------------------ */ \n\n\n/* ---------------------------------------------------------------------- */ function CrewView(param) {\n    let { crewId } = param;\n    var _crewInfo_vehicles, _crewInfo_departments;\n    _s();\n    /* ---------------- state / helpers ----------------------------------- */ const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"tab\", {\n        defaultValue: \"training\"\n    });\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dueTrainingCounter, setDueTrainingCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [taskList, setTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [voyages, setVoyages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSelf, setIsSelf] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [archiveOpen, setArchiveOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /* ---------------- data fetch ---------------------------------------- */ (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewMembersLogBookEntrySections)(crewId, setVoyages);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n    }, []);\n    /* vessels ------------------------------------------------------------ */ const handleSetVessels = (vsls)=>{\n        const activeVessels = vsls.filter((v)=>!v.archived);\n        setVessels(activeVessels.map((v)=>({\n                label: v.title,\n                value: v.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getVesselList)(handleSetVessels);\n    /* tasks -------------------------------------------------------------- */ const handleSetTaskList = (tasks)=>{\n        const active = tasks.filter((t)=>!t.archived).map((t)=>({\n                ...t,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.isOverDueTask)(t)\n            }));\n        const list = (0,_app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__.sortMaintenanceChecks)(active.map((mc)=>({\n                id: mc.id,\n                name: mc.name,\n                basicComponentID: mc.basicComponentID,\n                comments: mc.comments,\n                description: mc.description,\n                assignedToID: mc.assignedToID,\n                expires: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.upcomingScheduleDate)(mc),\n                status: mc.status,\n                startDate: mc.startDate,\n                isOverDue: mc.isOverDue,\n                basicComponent: mc.basicComponent,\n                isCompleted: mc.status === \"Completed\" ? \"1\" : \"2\"\n            })));\n        setTaskList(list);\n        setTaskCounter(active.filter((t)=>![\n                \"Completed\",\n                \"Save_As_Draft\"\n            ].includes(t.status) && ![\n                \"Completed\",\n                \"Upcoming\"\n            ].includes(t.isOverDue.status)).length);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getComponentMaintenanceCheckByMemberId)(crewId, handleSetTaskList);\n    /* crew info ---------------------------------------------------------- */ const handleSetCrewInfo = (info)=>{\n        var _withTraining_trainingSessionsDue;\n        setCrewInfo(info);\n        const [withTraining] = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            info\n        ], vessels);\n        var _withTraining_trainingSessionsDue_nodes_filter;\n        const dues = (_withTraining_trainingSessionsDue_nodes_filter = withTraining === null || withTraining === void 0 ? void 0 : (_withTraining_trainingSessionsDue = withTraining.trainingSessionsDue) === null || _withTraining_trainingSessionsDue === void 0 ? void 0 : _withTraining_trainingSessionsDue.nodes.filter((n)=>n.status.isOverdue || n.status.dueWithinSevenDays)) !== null && _withTraining_trainingSessionsDue_nodes_filter !== void 0 ? _withTraining_trainingSessionsDue_nodes_filter : [];\n        setDueTrainingCounter(dues.length);\n        if (localStorage.getItem(\"userId\") === info.id) setIsSelf(true);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewByID)(crewId, handleSetCrewInfo);\n    /* archive / retrieve user ------------------------------------------- */ const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>router.back(),\n        onError: (err)=>console.error(\"mutationUpdateUser error\", err)\n    });\n    const handleArchiveUser = async (info)=>{\n        if (!(info && info.id > 0)) return;\n        await mutationUpdateUser({\n            variables: {\n                input: {\n                    id: info.id,\n                    isArchived: !info.isArchived\n                }\n            }\n        });\n    };\n    /* permission helpers ------------------------------------------------- */ const noPerm = (perm)=>!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(perm, permissions);\n    const BadgeCounter = (param)=>{\n        let { count } = param;\n        return count ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"ml-2 flex h-5 w-5 items-center justify-center rounded-full border border-rose-600 bg-rose-100 text-xs font-medium text-rose-600\",\n            children: count\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 164,\n            columnNumber: 13\n        }, this) : null;\n    };\n    /* early exit if no access ------------------------------------------- */ if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER_CONTACT\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 176,\n            columnNumber: 13\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 178,\n            columnNumber: 13\n        }, this);\n    }\n    /* active log-book ---------------------------------------------------- */ const activeLog = voyages && voyages.length > 0 && !voyages[0].punchOut ? voyages[0] : null;\n    /* ----------------------- render ------------------------------------ */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col justify-between md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.H2, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 font-medium\",\n                                children: \"Crew:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1\",\n                                children: !crewInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__.Skeleton, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 29\n                                }, this) : \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: crewInfo.isArchived ? \"warning\" : activeLog ? \"warning\" : \"primary\",\n                                className: \"hidden min-w-fit rounded h-fit py-0.5 px-1.5 text-sm font-normal lg:inline ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    className: \"text-fire-bush-700 hover:text-fire-bush-800\",\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: crewInfo.isArchived ? \"warning\" : activeLog ? \"warning\" : \"primary\",\n                                className: \"block w-max rounded py-0.5 px-1.5 text-sm font-normal lg:hidden mb-2 ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    className: \"text-fire-bush-700 hover:text-fire-bush-800\",\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center justify-end gap-2\",\n                        children: [\n                            permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                                open: archiveOpen,\n                                onOpenChange: setArchiveOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            variant: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"primary\" : \"destructiveFilled\",\n                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                        className: \"sm:max-w-md\",\n                                        children: (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.DELETE_MEMBER || \"DELETE_MEMBER\", permissions) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: [\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\",\n                                                                \" \",\n                                                                \"User\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogDescription, {\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"retrieve\" : \"archive\",\n                                                                \" \",\n                                                                \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"this user\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\"),\n                                                                \" \",\n                                                                \"?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end gap-2 pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: \"outline\",\n                                                            onClick: ()=>setArchiveOpen(false),\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"primary\" : \"destructive\",\n                                                            onClick: ()=>{\n                                                                handleArchiveUser(crewInfo);\n                                                                setArchiveOpen(false);\n                                                            },\n                                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                        children: \"Warning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-3 text-slate-500\",\n                                                    children: \"You do not have permission to archive user.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setArchiveOpen(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 29\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/edit?id=\".concat(crewId)),\n                                children: \"Edit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 25\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/create\"),\n                                className: \"\".concat(tab === \"training\" ? \"hidden\" : \"\", \" \").concat(tab === \"qualification\" ? \"!mr-0\" : \"\"),\n                                children: \"Add Qualification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 25\n                            }, this),\n                            permissions && tab !== \"qualification\" && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/crew-training/create?memberId=\".concat(crewId)),\n                                children: \"Record Training\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 192,\n                columnNumber: 13\n            }, this),\n            ((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.email) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.vehicles) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.phoneNumber)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[1px] mt-2 mb-3 border-t border-b border-border px-4 pb-4 pt-4\",\n                children: [\n                    (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.primaryDuty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Primary Duty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ms-2\",\n                                children: crewInfo.primaryDuty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 25\n                    }, this),\n                    [\n                        \"email\",\n                        \"phoneNumber\"\n                    ].map((field)=>(permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.VIEW_MEMBER_CONTACT || \"VIEW_MEMBER_CONTACT\", permissions) || isSelf) && (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo[field]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-4 w-32\",\n                                    children: field === \"email\" ? \"Email:\" : \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-2\",\n                                    children: crewInfo[field]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, field, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 33\n                        }, this)),\n                    ((_crewInfo_vehicles = crewInfo.vehicles) === null || _crewInfo_vehicles === void 0 ? void 0 : _crewInfo_vehicles.nodes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Vessels:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 flex-wrap md:flex-nowrap\",\n                                children: crewInfo.vehicles.nodes.map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(v.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                            variant: \"primary\",\n                                            className: \"w-fit h-fit py-2 rounded-lg\",\n                                            children: v.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, v.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 25\n                    }, this),\n                    ((_crewInfo_departments = crewInfo.departments) === null || _crewInfo_departments === void 0 ? void 0 : _crewInfo_departments.nodes) && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Departments:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap md:flex-nowrap\",\n                                children: crewInfo.departments.nodes.map((d)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/department/info?id=\".concat(d.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ms-2 my-1 rounded border py-1 px-2 md:my-0\",\n                                            children: d.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, d.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 45\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 29\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 399,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.Tabs, {\n                    value: tab,\n                    onValueChange: setTab,\n                    className: \"pt-2 pb-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsList, {\n                            className: \"gap-2\",\n                            children: [\n                                (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"training\",\n                                    children: [\n                                        \"Training\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: dueTrainingCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                                    value: \"qualification\",\n                                                    disabled: true,\n                                                    children: \"Qualifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                            side: \"bottom\",\n                                            children: \"Coming soon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"allocatedTasks\",\n                                    children: [\n                                        \"Allocated Tasks\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: taskCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"voyages\",\n                                    children: \"Voyages\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"training\",\n                            children: noPerm(\"VIEW_MEMBER_TRAINING\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_list__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                memberId: crewId,\n                                excludeFilters: [\n                                    \"crew\",\n                                    \"overdueToggle\"\n                                ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"qualification\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"allocatedTasks\",\n                            children: noPerm(\"VIEW_MEMBER_TASKS\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                taskList: taskList\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"voyages\",\n                            children: noPerm(\"VIEW_MEMBER_VOYAGES\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_voyages__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                voyages: voyages\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 477,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n        lineNumber: 190,\n        columnNumber: 9\n    }, this);\n}\n_s(CrewView, \"A7VUUPb+Tfb3R8d4PeyBGNikOac=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = CrewView;\nvar _c;\n$RefreshReg$(_c, \"CrewView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/view.tsx\n"));

/***/ })

});